import { Router, Request, Response } from 'express';
import { 
  FlowExecution, 
  ExecuteFlowRequest, 
  ApiResponse 
} from '@rpa-project/shared';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { ExecutionService } from '../services/executionService';
import { QueueService } from '../queue/queueService';

const router = Router();
const executionService = new ExecutionService();
const queueService = new QueueService();

// GET /api/executions - Get all executions
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { flowId, status, limit = 20, offset = 0, startDate, endDate } = req.query;

  const executions = await executionService.getExecutions({
    flowId: flowId as string,
    status: status as any,
    limit: parseInt(limit as string),
    offset: parseInt(offset as string),
    startDate: startDate as string,
    endDate: endDate as string
  });
  
  const response: ApiResponse<FlowExecution[]> = {
    success: true,
    data: executions
  };
  
  res.json(response);
}));

// GET /api/executions/:id - Get execution by ID
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const execution = await executionService.getExecutionById(id);
  
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse<FlowExecution> = {
    success: true,
    data: execution
  };
  
  res.json(response);
}));

// POST /api/executions - Execute flow
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const executeRequest: ExecuteFlowRequest = req.body;
  
  if (!executeRequest.flowId) {
    throw createError('Flow ID is required', 400);
  }
  
  // Create execution record
  const execution = await executionService.createExecution(
    executeRequest.flowId,
    executeRequest.variables || {}
  );
  
  // Add to queue for processing
  await queueService.addFlowExecution(execution.id, {
    flowId: executeRequest.flowId,
    variables: executeRequest.variables || {}
  });
  
  const response: ApiResponse<FlowExecution> = {
    success: true,
    data: execution,
    message: 'Flow execution started'
  };
  
  res.status(201).json(response);
}));

// POST /api/executions/:id/cancel - Cancel execution
router.post('/:id/cancel', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
    throw createError('Cannot cancel execution that is already finished', 400);
  }
  
  // Cancel in queue
  await queueService.cancelExecution(id);
  
  // Update execution status
  await executionService.updateExecutionStatus(id, 'cancelled');
  
  const response: ApiResponse = {
    success: true,
    message: 'Execution cancelled'
  };
  
  res.json(response);
}));

// GET /api/executions/:id/logs - Get execution logs
router.get('/:id/logs', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse = {
    success: true,
    data: execution.logs
  };
  
  res.json(response);
}));

export { router as executionRoutes };
import { Router, Request, Response } from 'express';
import {
  FlowExecution,
  ExecuteFlowRequest,
  ApiResponse
} from '@rpa-project/shared';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { ExecutionService } from '../services/executionService';
import { QueueService } from '../queue/queueService';
import { vncService } from '../services/vncService';

const router = Router();
const executionService = new ExecutionService();
const queueService = new QueueService();

// GET /api/executions - Get all executions
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { flowId, status, limit = 20, offset = 0, startDate, endDate } = req.query;

  const executions = await executionService.getExecutions({
    flowId: flowId as string,
    status: status as any,
    limit: parseInt(limit as string),
    offset: parseInt(offset as string),
    startDate: startDate as string,
    endDate: endDate as string
  });
  
  const response: ApiResponse<FlowExecution[]> = {
    success: true,
    data: executions
  };
  
  res.json(response);
}));

// GET /api/executions/:id - Get execution by ID
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const execution = await executionService.getExecutionById(id);
  
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse<FlowExecution> = {
    success: true,
    data: execution
  };
  
  res.json(response);
}));

// POST /api/executions - Execute flow
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const executeRequest: ExecuteFlowRequest = req.body;

  if (!executeRequest.flowId) {
    throw createError('Flow ID is required', 400);
  }

  // Create execution record
  const execution = await executionService.createExecution(
    executeRequest.flowId,
    executeRequest.variables || {}
  );

  // Check if this is a manual execution that needs VNC
  let vncInfo: { vncUrl?: string; webUrl?: string } = {};

  if (executeRequest.isManualExecution) {
    // Get flow to check headless setting
    const { flowService } = await import('../services/flowService');
    const flow = await flowService.getFlowById(executeRequest.flowId);

    if (flow && flow.settings && flow.settings.headless === false) {
      console.log(`🖥️ Manual execution with headless=false detected for execution ${execution.id}`);

      try {
        vncInfo = await vncService.startVNCForExecution(execution.id);
        console.log(`✅ VNC services started for execution ${execution.id}`);
      } catch (error) {
        console.error(`❌ Failed to start VNC services for execution ${execution.id}:`, error);
        // Continue execution even if VNC fails
      }
    }
  }

  // Add to queue for processing
  await queueService.addFlowExecution(execution.id, {
    flowId: executeRequest.flowId,
    variables: executeRequest.variables || {},
    isManualExecution: executeRequest.isManualExecution,
    openVNCViewer: executeRequest.openVNCViewer
  });

  const response: ApiResponse<FlowExecution & { vncInfo?: typeof vncInfo }> = {
    success: true,
    data: {
      ...execution,
      ...(Object.keys(vncInfo).length > 0 && { vncInfo })
    },
    message: 'Flow execution started'
  };

  res.status(201).json(response);
}));

// POST /api/executions/:id/cancel - Cancel execution
router.post('/:id/cancel', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  if (execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled') {
    throw createError('Cannot cancel execution that is already finished', 400);
  }
  
  // Cancel in queue
  await queueService.cancelExecution(id);
  
  // Update execution status
  await executionService.updateExecutionStatus(id, 'cancelled');
  
  const response: ApiResponse = {
    success: true,
    message: 'Execution cancelled'
  };
  
  res.json(response);
}));

// GET /api/executions/:id/logs - Get execution logs
router.get('/:id/logs', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const execution = await executionService.getExecutionById(id);
  if (!execution) {
    throw createError('Execution not found', 404);
  }
  
  const response: ApiResponse = {
    success: true,
    data: execution.logs
  };
  
  res.json(response);
}));

export { router as executionRoutes };
